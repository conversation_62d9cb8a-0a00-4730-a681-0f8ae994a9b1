<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Sidebar Implementation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        /* Test Sidebar Styles - Clean Implementation */
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .sidebar {
            background-color: #343a40;
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            transition: width 0.3s ease;
            overflow-y: auto;
            z-index: 1000;
            padding: 15px 0;
        }

        .sidebar.collapsed {
            width: 60px;
        }

        /* Logo/Brand Section */
        .sidebar-header {
            padding: 0 15px 15px 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
        }

        .brand-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .brand-full {
            display: flex;
            align-items: center;
            transition: opacity 0.3s ease;
        }

        .brand-initials {
            display: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
            letter-spacing: 1px;
            margin: 0 auto;
        }

        .sidebar.collapsed .brand-full {
            display: none;
        }

        .sidebar.collapsed .brand-initials {
            display: flex;
        }

        /* Navigation Styles */
        .sidebar-nav {
            padding: 0;
            margin: 0;
            list-style: none;
        }

        .nav-item {
            margin: 2px 10px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateX(3px);
        }

        .nav-link.active {
            background-color: #007bff;
            color: white;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .nav-text {
            transition: opacity 0.3s ease;
            white-space: nowrap;
        }

        /* Collapsed State Styles */
        .sidebar.collapsed .nav-item {
            margin: 2px 5px;
        }

        .sidebar.collapsed .nav-link {
            padding: 12px 0;
            justify-content: center;
            position: relative;
        }

        .sidebar.collapsed .nav-icon {
            margin-right: 0;
            font-size: 20px;
        }

        .sidebar.collapsed .nav-text {
            display: none;
        }

        /* Tooltip for collapsed items */
        .sidebar.collapsed .nav-link:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            background: #343a40;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            white-space: nowrap;
            z-index: 1001;
            margin-left: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            font-size: 14px;
        }

        /* Toggle Button */
        .toggle-btn {
            position: fixed;
            bottom: 20px;
            left: 210px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1001;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .toggle-btn:hover {
            background-color: #0056b3;
            transform: scale(1.1);
        }

        .sidebar.collapsed .toggle-btn {
            left: 10px;
        }

        .toggle-btn i {
            transition: transform 0.3s ease;
        }

        .sidebar.collapsed .toggle-btn i {
            transform: rotate(180deg);
        }

        /* Main Content */
        .main-content {
            margin-left: 250px;
            padding: 20px;
            transition: margin-left 0.3s ease;
            min-height: 100vh;
            background-color: #f8f9fa;
        }

        .sidebar.collapsed + .main-content {
            margin-left: 60px;
        }

        /* Category Headers */
        .category-header {
            padding: 8px 15px;
            margin: 15px 10px 5px 10px;
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar.collapsed .category-header {
            display: none;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                width: 250px !important;
            }

            .sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0 !important;
            }

            .toggle-btn {
                display: none;
            }

            .mobile-toggle {
                display: block;
                position: fixed;
                top: 15px;
                left: 15px;
                z-index: 1002;
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 4px;
            }
        }

        .mobile-toggle {
            display: none;
        }
    </style>
</head>
<body>
    <!-- Mobile Toggle Button -->
    <button class="mobile-toggle" onclick="toggleMobileSidebar()">
        <i class="bi bi-list"></i>
    </button>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <!-- Header/Logo Section -->
        <div class="sidebar-header">
            <div class="brand-container">
                <div class="brand-full">
                    <h5 class="mb-0">Church Admin</h5>
                </div>
                <div class="brand-initials">
                    CA
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <ul class="sidebar-nav">
            <!-- Dashboard -->
            <li class="nav-item">
                <a href="#" class="nav-link active" data-tooltip="Dashboard">
                    <i class="bi bi-speedometer2 nav-icon"></i>
                    <span class="nav-text">Dashboard</span>
                </a>
            </li>

            <!-- Category: Member Management -->
            <div class="category-header">Member Management</div>
            <li class="nav-item">
                <a href="#" class="nav-link" data-tooltip="Members">
                    <i class="bi bi-people nav-icon"></i>
                    <span class="nav-text">Members</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link" data-tooltip="Add Member">
                    <i class="bi bi-person-plus nav-icon"></i>
                    <span class="nav-text">Add Member</span>
                </a>
            </li>

            <!-- Category: Email Management -->
            <div class="category-header">Email Management</div>
            <li class="nav-item">
                <a href="#" class="nav-link" data-tooltip="Email Templates">
                    <i class="bi bi-envelope nav-icon"></i>
                    <span class="nav-text">Email Templates</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link" data-tooltip="Bulk Email">
                    <i class="bi bi-send nav-icon"></i>
                    <span class="nav-text">Bulk Email</span>
                </a>
            </li>

            <!-- Category: Settings -->
            <div class="category-header">Settings</div>
            <li class="nav-item">
                <a href="#" class="nav-link" data-tooltip="General Settings">
                    <i class="bi bi-gear nav-icon"></i>
                    <span class="nav-text">General Settings</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link" data-tooltip="Security">
                    <i class="bi bi-shield-lock nav-icon"></i>
                    <span class="nav-text">Security</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- Toggle Button -->
    <button class="toggle-btn" onclick="toggleSidebar()">
        <i class="bi bi-chevron-left"></i>
    </button>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            <h1>Test Sidebar Implementation</h1>
            <p>This is a test implementation to demonstrate proper sidebar functionality:</p>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>Features Demonstrated</h5>
                        </div>
                        <div class="card-body">
                            <ul>
                                <li><strong>Collapsed Mode:</strong> Icons remain visible and centered</li>
                                <li><strong>Expanded Mode:</strong> Full text labels with icons</li>
                                <li><strong>Logo Handling:</strong> Shows initials "CA" when collapsed</li>
                                <li><strong>Smooth Transitions:</strong> Clean animations</li>
                                <li><strong>Tooltips:</strong> Hover over collapsed items to see labels</li>
                                <li><strong>Mobile Responsive:</strong> Proper mobile behavior</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>Test Instructions</h5>
                        </div>
                        <div class="card-body">
                            <ol>
                                <li>Click the toggle button (bottom left) to collapse/expand</li>
                                <li>Verify icons are visible in collapsed mode</li>
                                <li>Hover over collapsed items to see tooltips</li>
                                <li>Check that logo changes to initials when collapsed</li>
                                <li>Test on mobile devices for responsive behavior</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('collapsed');
            
            // Save state to localStorage
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        }

        function toggleMobileSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('mobile-open');
        }

        // Restore sidebar state on page load
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.getElementById('sidebar');
            const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            
            if (isCollapsed && window.innerWidth > 768) {
                sidebar.classList.add('collapsed');
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            const sidebar = document.getElementById('sidebar');
            if (window.innerWidth <= 768) {
                sidebar.classList.remove('collapsed');
            }
        });
    </script>
</body>
</html>
