<?php
// Get the current page filename to highlight the active menu item
$current_page = basename($_SERVER['PHP_SELF']);
?>

<!-- Sidebar -->
<div class="col-auto sidebar" id="sidebar">
    <div class="d-flex justify-content-between align-items-center mb-3 ps-2 pe-2 sidebar-header">
        <?php
        // Generate initials from admin title for collapsed mode
        $adminTitle = get_admin_title();
        $initials = '';
        $words = explode(' ', $adminTitle);
        foreach ($words as $word) {
            if (!empty($word)) {
                $initials .= strtoupper(substr($word, 0, 1));
            }
        }
        // Limit to 3 characters max
        $initials = substr($initials, 0, 3);
        if (empty($initials)) {
            $initials = 'CA'; // Default fallback
        }
        ?>

        <!-- Brand for expanded sidebar -->
        <div class="sidebar-brand-container">
            <h5 class="navbar-brand mb-0"><span class="navbar-brand-text"><?php echo $adminTitle; ?></span></h5>
        </div>

        <!-- Initials for collapsed sidebar -->
        <div class="sidebar-initials-container">
            <div class="sidebar-initials">
                <?php echo $initials; ?>
            </div>
        </div>

        <button class="d-md-none btn btn-sm btn-outline-light" id="sidebarToggle">
            <i class="bi bi-list"></i>
        </button>
    </div>
    <div class="sidebar-content">
        <ul class="nav flex-column">
            <!-- Dashboard -->
            <li class="nav-item">
                <a <?php echo ($current_page == 'index.php') ? 'class="active"' : ''; ?> href="<?php echo ADMIN_URL; ?>/index.php" title="Dashboard">
                    <i class="bi bi-speedometer2"></i> <span class="menu-text">Dashboard</span>
                </a>
            </li>
            
            <!-- Member Management -->
            <li class="nav-item mt-2 mb-1 category-header">
                <a href="#memberManagement" data-bs-toggle="collapse" aria-expanded="false" class="sidebar-heading d-flex justify-content-between align-items-center px-3 text-muted text-uppercase small">
                    <span>Member Management</span>
                    <i class="bi bi-chevron-down toggle-icon"></i>
                </a>
            </li>
            <div class="collapse" id="memberManagement">
                <li class="nav-item">
                    <a <?php echo ($current_page == 'members.php') ? 'class="active"' : ''; ?> href="<?php echo ADMIN_URL; ?>/members.php" title="Members">
                        <i class="bi bi-people"></i> <span class="menu-text">Members</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'add_member.php') ? 'class="active"' : ''; ?> href="<?php echo ADMIN_URL; ?>/add_member.php" title="Add Member">
                        <i class="bi bi-person-plus"></i> <span class="menu-text">Add Member</span>
                    </a>
                </li>
            </div>
            
            <!-- Email Management -->
            <li class="nav-item mt-2 mb-1 category-header">
                <a href="#emailManagement" data-bs-toggle="collapse" aria-expanded="false" class="sidebar-heading d-flex justify-content-between align-items-center px-3 text-muted text-uppercase small">
                    <span>Email Management</span>
                    <i class="bi bi-chevron-down toggle-icon"></i>
                </a>
            </li>
            <div class="collapse" id="emailManagement">
                <li class="nav-item">
                    <a <?php echo ($current_page == 'bulk_email.php') ? 'class="active"' : ''; ?> href="<?php echo ADMIN_URL; ?>/bulk_email.php" title="Bulk Email">
                        <i class="bi bi-envelope-fill"></i> <span class="menu-text">Bulk Email</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'contacts.php') ? 'class="active"' : ''; ?> href="<?php echo ADMIN_URL; ?>/contacts.php" title="Contact Management">
                        <i class="bi bi-person-lines-fill"></i> <span class="menu-text">Contacts</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'contact_groups.php') ? 'class="active"' : ''; ?> href="<?php echo ADMIN_URL; ?>/contact_groups.php" title="Contact Groups">
                        <i class="bi bi-folder"></i> <span class="menu-text">Contact Groups</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'birthday.php') ? 'class="active"' : ''; ?> href="<?php echo ADMIN_URL; ?>/birthday.php" title="Birthday Messages">
                        <i class="bi bi-gift"></i> <span class="menu-text">Birthday Messages</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'send_birthday_emails.php') ? 'class="active"' : ''; ?> href="<?php echo ADMIN_URL; ?>/send_birthday_emails.php" title="Send Birthday Emails">
                        <i class="bi bi-envelope-paper"></i> <span class="menu-text">Send Birthday Emails</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'test_birthday_email.php') ? 'class="active"' : ''; ?> href="<?php echo ADMIN_URL; ?>/test_birthday_email.php" title="Test Birthday Emails">
                        <i class="bi bi-envelope-check"></i> <span class="menu-text">Test Birthday Emails</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'debug_placeholders.php') ? 'class="active"' : ''; ?> href="<?php echo ADMIN_URL; ?>/debug_placeholders.php" title="Debug Email Placeholders">
                        <i class="bi bi-bug"></i> <span class="menu-text">Debug Placeholders</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'send_birthday_notification.php') ? 'class="active"' : ''; ?> href="<?php echo ADMIN_URL; ?>/send_birthday_notification.php" title="Birthday Notifications">
                        <i class="bi bi-bell"></i> <span class="menu-text">Birthday Notifications</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'email_templates.php') ? 'class="active"' : ''; ?> href="<?php echo ADMIN_URL; ?>/email_templates.php" title="Email Templates">
                        <i class="bi bi-envelope"></i> <span class="menu-text">Email Templates</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'email_scheduler.php') ? 'class="active"' : ''; ?> href="<?php echo ADMIN_URL; ?>/email_scheduler.php" title="Email Scheduler">
                        <i class="bi bi-calendar-event"></i> <span class="menu-text">Email Scheduler</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'automated_email_templates.php') ? 'class="active"' : ''; ?> href="<?php echo ADMIN_URL; ?>/automated_email_templates.php" title="Automated Email Templates">
                        <i class="bi bi-clock"></i> <span class="menu-text">Automated Templates</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'whatsapp_templates.php') ? 'class="active"' : ''; ?> href="<?php echo ADMIN_URL; ?>/whatsapp_templates.php" title="WhatsApp Templates">
                        <i class="bi bi-whatsapp"></i> <span class="menu-text">WhatsApp Templates</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'whatsapp_messages.php') ? 'class="active"' : ''; ?> href="<?php echo ADMIN_URL; ?>/whatsapp_messages.php" title="WhatsApp Messages">
                        <i class="bi bi-whatsapp"></i> <span class="menu-text">WhatsApp Messages</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'email_analytics.php') ? 'class="active"' : ''; ?> href="<?php echo ADMIN_URL; ?>/email_analytics.php" title="Email Analytics">
                        <i class="bi bi-graph-up"></i> <span class="menu-text">Email Analytics</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'email_settings.php') ? 'class="active"' : ''; ?> href="<?php echo ADMIN_URL; ?>/email_settings.php" title="Email Settings">
                        <i class="bi bi-gear"></i> <span class="menu-text">Email Settings</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'about_shortcodes.php') ? 'class="active"' : ''; ?> href="<?php echo ADMIN_URL; ?>/about_shortcodes.php" title="About & Shortcodes">
                        <i class="bi bi-info-circle"></i> <span class="menu-text">About & Shortcodes</span>
                    </a>
                </li>
            </div>
            
            <!-- Integrations -->
            <li class="nav-item mt-2 mb-1 category-header">
                <a href="#integrations" data-bs-toggle="collapse" aria-expanded="false" class="sidebar-heading d-flex justify-content-between align-items-center px-3 text-muted text-uppercase small">
                    <span>Integrations</span>
                    <i class="bi bi-chevron-down toggle-icon"></i>
                </a>
            </li>
            <div class="collapse" id="integrations">
                <li class="nav-item">
                    <a <?php echo ($current_page == 'calendar_integration.php') ? 'class="active"' : ''; ?> href="calendar_integration.php" title="Calendar Integration">
                        <i class="bi bi-calendar-check"></i> <span class="menu-text">Calendar Integration</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'social_media_integration.php') ? 'class="active"' : ''; ?> href="social_media_integration.php" title="Social Media Integration">
                        <i class="bi bi-share"></i> <span class="menu-text">Social Media</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'sms_integration.php') ? 'class="active"' : ''; ?> href="sms_integration.php" title="SMS Integration">
                        <i class="bi bi-chat-dots"></i> <span class="menu-text">SMS Integration</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'payment_integration.php') ? 'class="active"' : ''; ?> href="payment_integration.php" title="Payment Integration">
                        <i class="bi bi-credit-card"></i> <span class="menu-text">Payment Integration</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'payment_tables.php') ? 'class="active"' : ''; ?> href="payment_tables.php" title="Payment Tables Setup">
                        <i class="bi bi-table"></i> <span class="menu-text">Payment Tables</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'donations.php') ? 'class="active"' : ''; ?> href="donations.php" title="Manage Donations">
                        <i class="bi bi-cash-coin"></i> <span class="menu-text">Donations</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'check_payment_sdks.php') ? 'class="active"' : ''; ?> href="check_payment_sdks.php" title="Check Payment SDKs">
                        <i class="bi bi-check-circle"></i> <span class="menu-text">Check Payment SDKs</span>
                    </a>
                </li>
            </div>
            
            <!-- Account -->
            <li class="nav-item mt-2 mb-1 category-header">
                <a href="#account" data-bs-toggle="collapse" aria-expanded="false" class="sidebar-heading d-flex justify-content-between align-items-center px-3 text-muted text-uppercase small">
                    <span>Account</span>
                    <i class="bi bi-chevron-down toggle-icon"></i>
                </a>
            </li>
            <div class="collapse" id="account">
                <li class="nav-item">
                    <a <?php echo ($current_page == 'site_settings.php') ? 'class="active"' : ''; ?> href="<?php echo ADMIN_URL; ?>/site_settings.php" title="Site Settings">
                        <i class="bi bi-sliders"></i> <span class="menu-text">Site Settings</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'setup_security.php') ? 'class="active"' : ''; ?> href="setup_security.php" title="Security Setup">
                        <i class="bi bi-shield-plus"></i> <span class="menu-text">Security Setup</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'security_audit.php') ? 'class="active"' : ''; ?> href="security_audit.php" title="Security Audit">
                        <i class="bi bi-shield-lock"></i> <span class="menu-text">Security Audit</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'security_settings.php') ? 'class="active"' : ''; ?> href="security_settings.php" title="Security Settings">
                        <i class="bi bi-shield-check"></i> <span class="menu-text">Security Settings</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a <?php echo ($current_page == 'profile.php') ? 'class="active"' : ''; ?> href="<?php echo ADMIN_URL; ?>/profile.php" title="My Profile">
                        <i class="bi bi-person-circle"></i> <span class="menu-text">My Profile</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo ADMIN_URL; ?>/logout.php" title="Logout">
                        <i class="bi bi-box-arrow-right"></i> <span class="menu-text">Logout</span>
                    </a>
                </li>
            </div>
        </ul>
    </div>
    
    <!-- Collapse toggle button -->
    <button id="sidebarCollapseToggle" title="Toggle Sidebar">
        <i class="bi bi-chevron-left"></i>
    </button>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');
    const collapseToggle = document.getElementById('sidebarCollapseToggle');
    const mainContent = document.querySelector('.main-content');
    const isMobile = window.innerWidth <= 768;
    
    // Mobile sidebar toggle
    if(sidebarToggle) {
        sidebarToggle.addEventListener('click', function(e) {
            e.preventDefault();
            sidebar.classList.toggle('sidebar-collapsed');
        });
    }
    
    // Desktop sidebar toggle - Only apply for non-mobile
    if(!isMobile) {
        // Check for user preference in localStorage
        if(localStorage.getItem('sidebarCollapsed') === 'true') {
            sidebar.classList.add('collapsed');
            mainContent.classList.add('expanded');
            if(collapseToggle && collapseToggle.querySelector('i')) {
                collapseToggle.querySelector('i').classList.remove('bi-chevron-left');
                collapseToggle.querySelector('i').classList.add('bi-chevron-right');
            }
        }
        
        // Sidebar collapse/expand for desktop
        if(collapseToggle) {
            collapseToggle.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
                
                // Update icon
                const icon = this.querySelector('i');
                if(icon) {
                    if(sidebar.classList.contains('collapsed')) {
                        icon.classList.remove('bi-chevron-left');
                        icon.classList.add('bi-chevron-right');
                        localStorage.setItem('sidebarCollapsed', 'true');
                    } else {
                        icon.classList.remove('bi-chevron-right');
                        icon.classList.add('bi-chevron-left');
                        localStorage.setItem('sidebarCollapsed', 'false');
                    }
                }
                
                // Force window resize event to update any responsive components
                window.dispatchEvent(new Event('resize'));
            });
        }
    }
    
    // Auto-expand the category that contains the current active page
    const activeLink = document.querySelector('.nav-item a.active');
    if (activeLink) {
        const parentCollapse = activeLink.closest('.collapse');
        if (parentCollapse) {
            parentCollapse.classList.add('show');
            const categoryHeader = document.querySelector(`[href="#${parentCollapse.id}"]`);
            if (categoryHeader && categoryHeader.querySelector('.toggle-icon')) {
                categoryHeader.querySelector('.toggle-icon').classList.remove('bi-chevron-down');
                categoryHeader.querySelector('.toggle-icon').classList.add('bi-chevron-up');
                categoryHeader.setAttribute('aria-expanded', 'true');
            }
        }
    }
    
    // Toggle category collapse icons
    const categoryHeaders = document.querySelectorAll('.category-header a');
    categoryHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const isExpanded = this.getAttribute('aria-expanded') === 'true';
            const icon = this.querySelector('.toggle-icon');
            
            if (icon) {
                if (isExpanded) {
                    icon.classList.remove('bi-chevron-up');
                    icon.classList.add('bi-chevron-down');
                } else {
                    icon.classList.remove('bi-chevron-down');
                    icon.classList.add('bi-chevron-up');
                }
            }
        });
    });
});
</script> 